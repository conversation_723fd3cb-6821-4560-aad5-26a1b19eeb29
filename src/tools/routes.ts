import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { z } from "zod";
import { NocoBaseClient, type DesktopRoute } from "../client.js";

// 页面模板定义
export const PAGE_TEMPLATES = {
  blank: {
    name: "空白页面",
    description: "创建一个空白页面",
    schema: {
      type: "void",
      "x-component": "Page",
      "x-component-props": {},
      properties: {
        grid: {
          type: "void",
          "x-component": "Grid",
          "x-initializer": "page:addBlock"
        }
      }
    }
  },
  table: {
    name: "表格页面",
    description: "创建一个包含数据表格的页面",
    schema: (collectionName?: string) => ({
      type: "void",
      "x-component": "Page",
      properties: {
        grid: {
          type: "void",
          "x-component": "Grid",
          properties: {
            table: {
              type: "void",
              "x-component": "TableBlockProvider",
              "x-component-props": {
                collection: collectionName || "users",
                action: "list"
              },
              properties: {
                table: {
                  type: "array",
                  "x-component": "TableV2",
                  "x-component-props": {
                    rowKey: "id",
                    rowSelection: {
                      type: "checkbox"
                    }
                  }
                }
              }
            }
          }
        }
      }
    })
  },
  dashboard: {
    name: "仪表板页面",
    description: "创建一个仪表板页面",
    schema: {
      type: "void",
      "x-component": "Page",
      properties: {
        grid: {
          type: "void",
          "x-component": "Grid",
          "x-initializer": "page:addBlock",
          properties: {
            stats: {
              type: "void",
              "x-component": "CardItem",
              "x-component-props": {
                title: "统计信息"
              }
            }
          }
        }
      }
    }
  }
};

// 生成唯一 UID
function generateUID(prefix: string): string {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
}

// 注册路由工具
export async function registerRouteTools(server: McpServer, client: NocoBaseClient): Promise<void> {
  // List routes tool
  server.registerTool(
    "list_routes",
    {
      title: "List Routes",
      description: "List all routes in NocoBase with optional tree structure",
      inputSchema: {
        tree: z.boolean().optional().describe("Return routes in tree structure").default(true)
      }
    },
    async ({ tree = true }) => {
      try {
        const routes = await client.listRoutes({ tree });
        return {
          content: [{
            type: "text",
            text: `Found ${routes.length} routes:\n\n${JSON.stringify(routes, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error listing routes: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Dump raw accessible routes response (debug)
  server.registerTool(
    "routes_dump_raw",
    {
      title: "Dump Raw Accessible Routes",
      description: "Dump the raw JSON returned by desktopRoutes:listAccessible?tree=true&sort=sort",
      inputSchema: {}
    },
    async () => {
      try {
        const routes = await client.listRoutes({ tree: true });
        return {
          content: [{ type: 'text', text: JSON.stringify(routes, null, 2) }]
        };
      } catch (error) {
        return {
          content: [{ type: 'text', text: `Error: ${error instanceof Error ? error.message : String(error)}` }],
          isError: true
        };
      }
    }
  );


  // Proxy raw API for accessible routes (debug)
  server.registerTool(
    "routes_dump_raw_direct",
    {
      title: "Dump Raw Accessible Routes (Direct)",
      description: "Directly call /desktopRoutes:listAccessible?tree=true&sort=sort and return raw response",
      inputSchema: {}
    },
    async () => {
      try {
        const data = await (client as any).rawGet('/desktopRoutes:listAccessible?tree=true&sort=sort');
        return { content: [{ type: 'text', text: typeof data === 'string' ? data : JSON.stringify(data, null, 2) }] };
      } catch (error) {
        return { content: [{ type: 'text', text: `Error: ${error instanceof Error ? error.message : String(error)}` }], isError: true };
      }
    }
  );


  // Proxy raw API for full routes list (admin)
  server.registerTool(
    "routes_dump_raw_direct_all",
    {
      title: "Dump Raw All Routes (Direct)",
      description: "Directly call /desktopRoutes:list?tree=true&sort=sort and return raw response",
      inputSchema: {}
    },
    async () => {
      try {
        const data = await (client as any).rawGet('/desktopRoutes:list?tree=true&sort=sort');
        return { content: [{ type: 'text', text: typeof data === 'string' ? data : JSON.stringify(data, null, 2) }] };
      } catch (error) {
        return { content: [{ type: 'text', text: `Error: ${error instanceof Error ? error.message : String(error)}` }], isError: true };
      }
    }
  );


  // Routes tree overview tool
  server.registerTool(
    "routes_tree_overview",
    {
      title: "Routes Tree Overview",
      description: "Print a human-readable tree of current routes",
      inputSchema: {
        includeIds: z.boolean().optional().describe("Include route IDs in output").default(false),
        includeHidden: z.boolean().optional().describe("Include hidden routes").default(false)
      }
    },
    async ({ includeIds = false, includeHidden = false }) => {
      try {
        const routes = await client.listRoutes({ tree: true });

        function formatNode(node: DesktopRoute, depth: number): string {
          if (node.hidden && !includeHidden) return '';
          const indent = '  '.repeat(depth);
          const typeEmoji = node.type === 'group' ? '📂' : node.type === 'page' ? '📄' : node.type === 'link' ? '🔗' : '📁';
          const idPart = includeIds && node.id !== undefined ? ` [${node.id}]` : '';
          const iconPart = node.icon ? ` (${node.icon})` : '';
          let line = `${indent}${typeEmoji} ${node.title}${iconPart}${idPart}`;
          if (node.type === 'link' && node.options?.href) {
            line += ` -> ${node.options.href}`;
          }
          const children = node.children || [];
          const childLines = children
            .map((c) => formatNode(c, depth + 1))
            .filter(Boolean)
            .join('\n');
          return childLines ? `${line}\n${childLines}` : line;
        }

        const text = Array.isArray(routes)
          ? routes.map((r) => formatNode(r, 0)).filter(Boolean).join('\n')
          : JSON.stringify(routes, null, 2);

        return {
          content: [{ type: 'text', text }]
        };
      } catch (error) {
        return {
          content: [{ type: 'text', text: `Error generating routes overview: ${error instanceof Error ? error.message : String(error)}` }],
          isError: true
        };
      }
    }
  );

  // Get route tool
  server.registerTool(
    "get_route",
    {
      title: "Get Route",
      description: "Get detailed information about a specific route",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("Route ID to retrieve")
      }
    },
    async ({ id }) => {
      try {
        const route = await client.getRoute(id);
        return {
          content: [{
            type: "text",
            text: `Route details:\n\n${JSON.stringify(route, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error getting route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // Create page route tool
  server.registerTool(
    "create_page_route",
    {
      title: "Create Page Route",
      description: "Create a new page route in NocoBase",
      inputSchema: {
        title: z.string().describe("Page title"),
        parentId: z.union([z.string(), z.number()]).optional().describe("Parent route ID (for nested routes)"),
        icon: z.string().optional().describe("Ant Design icon name (e.g., 'UserOutlined', 'SettingOutlined')"),
        template: z.enum(["blank", "table", "dashboard"]).optional().default("blank").describe("Page template type"),
        collectionName: z.string().optional().describe("Collection name for table template"),
        enableTabs: z.boolean().optional().default(false).describe("Enable tabs for this page"),
        hidden: z.boolean().optional().default(false).describe("Hide this route from menu")
      }
    },
    async ({ title, parentId, icon, template = "blank", collectionName, enableTabs = false, hidden = false }) => {
      try {
        const pageSchemaUid = generateUID("page");
        const menuSchemaUid = generateUID("menu");
        const tabSchemaUid = generateUID("tab");
        const tabSchemaName = generateUID("tab");

        // 1. 创建页面 schema（使用NocoBase官方的getPageMenuSchema结构）
        const pageSchema = {
          type: "void",
          "x-component": "Page",
          properties: {
            [tabSchemaName]: {
              type: "void",
              "x-component": "Grid",
              "x-initializer": "page:addBlock",
              properties: {},
              "x-uid": tabSchemaUid,
              "x-async": true,
            },
          },
          "x-uid": pageSchemaUid,
        };

        await client.createPageSchema(pageSchemaUid, pageSchema);

        // 2. 创建路由记录（包含tabs子路由）
        const pageRoute: Partial<DesktopRoute> = {
          type: "page",
          title,
          schemaUid: pageSchemaUid,
          menuSchemaUid: menuSchemaUid,
          ...(parentId && { parentId }),
          ...(icon && { icon }),
          enableTabs,
          hidden,
          children: [
            {
              type: "tabs",
              title: "", // tabs子路由通常没有标题
              schemaUid: tabSchemaUid,
              tabSchemaName: tabSchemaName,
              hidden: true, // tabs子路由通常是隐藏的
              sort: 1
            }
          ]
        };

        const result = await client.createRoute(pageRoute);

        // 3. 创建菜单项schema（插入到主菜单或父菜单中）
        const menuItemSchema = {
          type: "void",
          title,
          'x-component': "Menu.Item",
          'x-designer': "Menu.Item.Designer",
          'x-component-props': {
            icon,
            __route__: result
          },
          'x-uid': menuSchemaUid,
          'x-async': false
        };

        // 确定父菜单UID - 如果有parentId，需要查找父路由的menuSchemaUid
        let parentMenuUid = "nocobase-admin-menu"; // 默认插入到根菜单
        if (parentId) {
          try {
            const parentRoute = await client.getRoute(parentId);
            if (parentRoute.menuSchemaUid) {
              parentMenuUid = parentRoute.menuSchemaUid;
            } else if (parentRoute.schemaUid) {
              parentMenuUid = parentRoute.schemaUid;
            }
          } catch (error) {
            console.warn(`Failed to get parent route ${parentId}, using root menu`);
          }
        }

        await client.createMenuItemSchema(parentMenuUid, menuItemSchema);
        return {
          content: [{
            type: "text",
            text: `Page route created successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating page route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // Create group route tool
  server.registerTool(
    "create_group_route",
    {
      title: "Create Group Route",
      description: "Create a new group route (menu group) in NocoBase",
      inputSchema: {
        title: z.string().describe("Group title"),
        parentId: z.union([z.string(), z.number()]).optional().describe("Parent route ID (for nested groups)"),
        icon: z.string().optional().describe("Ant Design icon name (e.g., 'AppstoreOutlined', 'SettingOutlined')"),
        children: z.array(z.object({
          type: z.enum(["page", "link", "group"]),
          title: z.string(),
          icon: z.string().optional(),
          href: z.string().optional(),
          openInNewWindow: z.boolean().optional()
        })).optional().describe("Child routes to create within this group")
      }
    },
    async ({ title, parentId, icon, children }) => {
      try {
        const groupSchemaUid = generateUID("group");

        // 1. 创建路由记录
        const groupRoute: Partial<DesktopRoute> = {
          type: "group",
          title,
          schemaUid: groupSchemaUid,
          ...(parentId && { parentId }),
          ...(icon && { icon }),
          ...(children && { children: children as DesktopRoute[] })
        };

        const result = await client.createRoute(groupRoute);

        // 2. 创建分组菜单项schema（插入到主菜单或父菜单中）
        const groupMenuSchema = {
          type: "void",
          title,
          'x-component': "Menu.SubMenu",
          'x-designer': "Menu.SubMenu.Designer",
          'x-component-props': {
            icon,
            __route__: result
          },
          'x-uid': groupSchemaUid,
          'x-async': false,
          properties: {} // 分组菜单需要properties来容纳子菜单项
        };

        // 确定父菜单UID
        let parentMenuUid = "nocobase-admin-menu"; // 默认插入到根菜单
        if (parentId) {
          try {
            const parentRoute = await client.getRoute(parentId);
            if (parentRoute.menuSchemaUid) {
              parentMenuUid = parentRoute.menuSchemaUid;
            } else if (parentRoute.schemaUid) {
              parentMenuUid = parentRoute.schemaUid;
            }
          } catch (error) {
            console.warn(`Failed to get parent route ${parentId}, using root menu`);
          }
        }

        await client.createMenuItemSchema(parentMenuUid, groupMenuSchema);
        return {
          content: [{
            type: "text",
            text: `Group route created successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating group route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // Create link route tool
  server.registerTool(
    "create_link_route",
    {
      title: "Create Link Route",
      description: "Create a new link route in NocoBase",
      inputSchema: {
        title: z.string().describe("Link title"),
        href: z.string().describe("URL to link to"),
        parentId: z.union([z.string(), z.number()]).optional().describe("Parent route ID (for nested links)"),
        icon: z.string().optional().describe("Ant Design icon name (e.g., 'LinkOutlined', 'GlobalOutlined')"),
        openInNewWindow: z.boolean().optional().default(true).describe("Open link in new window"),
        params: z.array(z.object({
          name: z.string(),
          value: z.string()
        })).optional().describe("URL parameters")
      }
    },
    async ({ title, href, parentId, icon, openInNewWindow = true, params }) => {
      try {
        const linkSchemaUid = generateUID("link");

        // 1. 创建路由记录
        const linkRoute: Partial<DesktopRoute> = {
          type: "link",
          title,
          schemaUid: linkSchemaUid,
          ...(parentId && { parentId }),
          ...(icon && { icon }),
          options: {
            href,
            openInNewWindow,
            ...(params && { params })
          }
        };

        const result = await client.createRoute(linkRoute);

        // 2. 创建链接菜单项schema（插入到主菜单或父菜单中）
        const linkMenuSchema = {
          type: "void",
          title,
          'x-component': "Menu.Item",
          'x-designer': "Menu.Item.Designer",
          'x-component-props': {
            icon,
            __route__: result
          },
          'x-uid': linkSchemaUid,
          'x-async': false
        };

        // 确定父菜单UID
        let parentMenuUid = "nocobase-admin-menu"; // 默认插入到根菜单
        if (parentId) {
          try {
            const parentRoute = await client.getRoute(parentId);
            if (parentRoute.menuSchemaUid) {
              parentMenuUid = parentRoute.menuSchemaUid;
            } else if (parentRoute.schemaUid) {
              parentMenuUid = parentRoute.schemaUid;
            }
          } catch (error) {
            console.warn(`Failed to get parent route ${parentId}, using root menu`);
          }
        }

        await client.createMenuItemSchema(parentMenuUid, linkMenuSchema);
        return {
          content: [{
            type: "text",
            text: `Link route created successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating link route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Update route tool
  server.registerTool(
    "update_route",
    {
      title: "Update Route",
      description: "Update an existing route",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("Route ID to update"),
        title: z.string().optional().describe("New title"),
        icon: z.string().optional().describe("New icon"),
        hidden: z.boolean().optional().describe("Hide/show route"),
        enableHeader: z.boolean().optional().describe("Enable page header (title bar)"),
        displayTitle: z.boolean().optional().describe("Display the page title in header"),
        href: z.string().optional().describe("New URL (for link routes)"),
        openInNewWindow: z.boolean().optional().describe("Open in new window (for link routes)")
      }
    },
    async ({ id, title, icon, hidden, enableHeader, displayTitle, href, openInNewWindow }) => {
      try {
        const updates: Partial<DesktopRoute> = {};
        if (title) updates.title = title;
        if (icon) updates.icon = icon;
        if (hidden !== undefined) updates.hidden = hidden;

        // route options
        const options: any = {};
        if (href) options.href = href;
        if (openInNewWindow !== undefined) options.openInNewWindow = openInNewWindow;
        if (enableHeader !== undefined) options.enableHeader = enableHeader;
        if (displayTitle !== undefined) options.displayTitle = displayTitle;
        if (Object.keys(options).length > 0) {
          (updates as any).options = options;
        }

        const result = await client.updateRoute(id, updates);
        return {
          content: [{
            type: "text",
            text: `Route updated successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error updating route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
  // Delete route tool
  server.registerTool(
    "delete_route",
    {
      title: "Delete Route",
      description: "Delete a route and all its children",
      inputSchema: {
        id: z.union([z.string(), z.number()]).describe("Route ID to delete")
      }
    },
    async ({ id }) => {
      try {
        await client.deleteRoute(id);
        return {
          content: [{
            type: "text",
            text: `Route ${id} deleted successfully`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error deleting route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Move route tool
  server.registerTool(
    "move_route",
    {
      title: "Move Route",
      description: "Move a route to a different position or parent. Can move to: before/after another route, or inside a group",
      inputSchema: {
        sourceId: z.union([z.string(), z.number()]).describe("Route ID to move"),
        targetId: z.union([z.string(), z.number()]).optional().describe("Target route ID for reference"),
        position: z.enum(["before", "after", "inside", "first", "last"]).describe("Position relative to target: 'before' (insert before target), 'after' (insert after target), 'inside' (move into target group), 'first' (move to first position in target group), 'last' (move to last position in target group)"),
        newParentId: z.union([z.string(), z.number()]).optional().describe("New parent ID (for changing parent, use with 'first' or 'last' position)")
      }
    },
    async ({ sourceId, targetId, position, newParentId }) => {
      try {
        // 获取源路由信息
        const sourceRoute = await client.getRoute(sourceId);

        let method: 'insertAfter' | 'prepend' = 'insertAfter';
        let actualTargetId = targetId;
        let parentId = newParentId;

        // 根据position确定移动方式
        switch (position) {
          case 'before':
            if (!targetId) throw new Error("targetId is required for 'before' position");
            method = 'prepend';
            actualTargetId = targetId;
            break;
          case 'after':
            if (!targetId) throw new Error("targetId is required for 'after' position");
            method = 'insertAfter';
            actualTargetId = targetId;
            break;
          case 'inside':
            if (!targetId) throw new Error("targetId is required for 'inside' position");
            // 移动到目标分组内部的最后位置
            parentId = targetId;
            method = 'insertAfter';
            actualTargetId = undefined;
            break;
          case 'first':
            method = 'prepend';
            actualTargetId = undefined;
            break;
          case 'last':
            method = 'insertAfter';
            actualTargetId = undefined;
            break;
        }

        // 如果需要更改父级，先更新路由的parentId
        if (parentId !== undefined && parentId !== sourceRoute.parentId) {
          await client.updateRoute(sourceId, { parentId });
        }

        // 执行路由移动（排序）
        if (actualTargetId !== undefined) {
          await client.moveRoute({
            sourceId,
            targetId: actualTargetId,
            method
          });
        }

        // 如果路由有菜单schema，也需要移动菜单项
        if (sourceRoute.menuSchemaUid) {
          try {
            let targetParentUid = "nocobase-admin-menu"; // 默认根菜单
            let menuPosition: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd' = 'beforeEnd';

            // 确定目标菜单父级
            if (parentId) {
              const parentRoute = await client.getRoute(parentId);
              if (parentRoute.menuSchemaUid) {
                targetParentUid = parentRoute.menuSchemaUid;
              } else if (parentRoute.schemaUid) {
                targetParentUid = parentRoute.schemaUid;
              }
            } else if (targetId && (position === 'before' || position === 'after')) {
              const targetRoute = await client.getRoute(targetId);
              // 获取目标路由的父菜单
              if (targetRoute.parentId) {
                const targetParentRoute = await client.getRoute(targetRoute.parentId);
                if (targetParentRoute.menuSchemaUid) {
                  targetParentUid = targetParentRoute.menuSchemaUid;
                } else if (targetParentRoute.schemaUid) {
                  targetParentUid = targetParentRoute.schemaUid;
                }
              }
            }

            // 确定菜单插入位置
            switch (position) {
              case 'before':
              case 'first':
                menuPosition = 'afterBegin';
                break;
              case 'after':
              case 'last':
              case 'inside':
                menuPosition = 'beforeEnd';
                break;
            }

            // 移动菜单项schema
            await client.moveMenuItemSchema(sourceRoute.menuSchemaUid, targetParentUid, menuPosition);
          } catch (error) {
            console.warn('Failed to move menu schema, but route move succeeded:', error);
          }
        }

        return {
          content: [{
            type: "text",
            text: `Route ${sourceId} moved successfully to ${position}${targetId ? ` target ${targetId}` : ''}${parentId ? ` in parent ${parentId}` : ''}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error moving route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );

  // Create tabs route tool
  server.registerTool(
    "create_tabs_route",
    {
      title: "Create Tabs Route",
      description: "Create a tabs sub-route under a parent page (like page_1 structure)",
      inputSchema: {
        parentId: z.union([z.string(), z.number()]).describe("Parent route ID"),
        tabSchemaName: z.string().optional().describe("Tab schema name")
      }
    },
    async ({ parentId, tabSchemaName }) => {
      try {
        const schemaName = tabSchemaName || generateUID("tab");
        const schemaUid = generateUID("tabs");

        // 创建 tabs 子路由的 schema（直接是 Grid 组件）
        const tabsSchema = {
          type: "void",
          "x-component": "Grid",
          "x-initializer": "page:addBlock",
          "x-async": true,
          "x-index": 1
        };

        // 创建 UI Schema
        await client.createPageSchema(schemaUid, tabsSchema);

        // 创建 tabs 路由
        const tabsRoute: Partial<DesktopRoute> = {
          type: "tabs",
          parentId,
          schemaUid: schemaUid,
          tabSchemaName: schemaName,
          hidden: true, // tabs 子路由通常是隐藏的
          sort: 1
        };

        const result = await client.createRoute(tabsRoute);
        return {
          content: [{
            type: "text",
            text: `Tabs route created successfully:\n\n${JSON.stringify(result, null, 2)}`
          }]
        };
      } catch (error) {
        return {
          content: [{
            type: "text",
            text: `Error creating tabs route: ${error instanceof Error ? error.message : String(error)}`
          }],
          isError: true
        };
      }
    }
  );
}
