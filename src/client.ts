import axios, { type AxiosInstance, type AxiosResponse } from "axios";

export interface NocoBaseConfig {
  baseUrl: string;
  token: string;
  app: string;
}

export interface Collection {
  key: string;
  name: string;
  title: string;
  inherit: boolean;
  hidden: boolean;
  description?: string;
  autoGenId: boolean;
  createdAt: boolean;
  updatedAt: boolean;
  createdBy: boolean;
  updatedBy: boolean;
  filterTargetKey: string;
  unavailableActions: string[];
  fields?: Field[];
}

export interface Field {
  key: string;
  name: string;
  type: string;
  interface: string;
  collectionName: string;
  description?: string;
  uiSchema?: any;
  [key: string]: any;
}

export interface Record {
  id: number | string;
  [key: string]: any;
}

export interface DesktopRoute {
  id?: number | string;
  parentId?: number | string;
  type: 'page' | 'tab' | 'tabs' | 'group' | 'link';
  title: string;
  icon?: string;
  tooltip?: string;
  schemaUid?: string;
  menuSchemaUid?: string;
  tabSchemaName?: string;
  pageSchemaUid?: string;
  sort?: number;
  options?: {
    href?: string;
    params?: Array<{ name: string; value: any }>;
    openInNewWindow?: boolean;
    [key: string]: any;
  };
  enableTabs?: boolean;
  enableHeader?: boolean;
  displayTitle?: boolean;
  hidden?: boolean;
  hideInMenu?: boolean;
  children?: DesktopRoute[];
  roles?: Array<{ name: string; title: string }>;
  createdAt?: string;
  updatedAt?: string;
  createdBy?: any;
  updatedBy?: any;
  [key: string]: any;
}

export interface ApiResponse<T = any> {
  data: T;
  meta?: {
    count: number;
    page: number;
    pageSize: number;
    totalPage: number;
  };
}

export class NocoBaseClient {
  private client: AxiosInstance;
  private config: NocoBaseConfig;

  constructor(config: NocoBaseConfig) {
    this.config = config;
    const baseURL = config.baseUrl.endsWith('/') ? config.baseUrl : config.baseUrl + '/';
    this.client = axios.create({
      baseURL,
      headers: {
        "Authorization": `Bearer ${config.token}`,
        "X-App": config.app,
        "Content-Type": "application/json",
      },
      timeout: 30000,
    });

    // Normalize request URLs to respect baseURL path prefix
    this.client.interceptors.request.use((config) => {
      if (typeof config.url === 'string') {
        // Ensure relative URLs so axios preserves baseURL path (e.g. /yc/api)
        if (config.url.startsWith('/')) {
          config.url = config.url.slice(1);
        }
      }
      return config;
    });

    // Add response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response) {
          const message = error.response.data?.errors?.[0]?.message || error.response.statusText;
          throw new Error(`NocoBase API Error (${error.response.status}): ${message}`);
        } else if (error.request) {
          throw new Error("NocoBase API Error: No response received");
        } else {
          throw new Error(`NocoBase API Error: ${error.message}`);
        }
      }
    );
  }

  // Collections API
  async listCollections(): Promise<Collection[]> {
    const response: AxiosResponse<ApiResponse<Collection[]>> = await this.client.get("/collections:list");
    return response.data.data;
  }

  async listCollectionsMeta(): Promise<Collection[]> {
    const response: AxiosResponse<ApiResponse<Collection[]>> = await this.client.get("/collections:listMeta");
    return response.data.data;
  }

  async getCollection(name: string): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.get(
      `/collections:get?filterByTk=${name}&appends[]=fields`
    );
    return response.data.data;
  }

  async createCollection(collection: Partial<Collection>): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.post(
      "/collections:create",
      collection
    );
    return response.data.data;
  }

  async updateCollection(name: string, updates: Partial<Collection>): Promise<Collection> {
    const response: AxiosResponse<ApiResponse<Collection>> = await this.client.post(
      `/collections:update?filterByTk=${name}`,
      updates
    );
    return response.data.data;
  }

  async deleteCollection(name: string): Promise<void> {
    await this.client.post(`/collections:destroy?filterByTk=${name}`);
  }

  // Fields API
  async listFields(collectionName: string): Promise<Field[]> {
    const response: AxiosResponse<ApiResponse<Field[]>> = await this.client.get(
      `/collections/${collectionName}/fields:list`
    );
    return response.data.data;
  }

  async createField(collectionName: string, field: Partial<Field>): Promise<Field> {
    const response: AxiosResponse<ApiResponse<Field>> = await this.client.post(
      `/collections/${collectionName}/fields:create`,
      field
    );
    return response.data.data;
  }

  // Records API
  async listRecords(collectionName: string, options?: {
    page?: number;
    pageSize?: number;
    filter?: any;
    sort?: string[];
    appends?: string[];
  }): Promise<{ data: Record[]; meta?: any }> {
    const params = new URLSearchParams();

    if (options?.page) params.append("page", options.page.toString());
    if (options?.pageSize) params.append("pageSize", options.pageSize.toString());
    if (options?.filter) params.append("filter", JSON.stringify(options.filter));
    if (options?.sort) {
      options.sort.forEach(s => params.append("sort[]", s));
    }
    if (options?.appends) {
      options.appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Record[]>> = await this.client.get(
      `/${collectionName}:list?${params.toString()}`
    );
    return { data: response.data.data, meta: response.data.meta };
  }

  async getRecord(collectionName: string, id: string | number, appends?: string[]): Promise<Record> {
    const params = new URLSearchParams();
    params.append("filterByTk", id.toString());

    if (appends) {
      appends.forEach(a => params.append("appends[]", a));
    }

    const response: AxiosResponse<ApiResponse<Record>> = await this.client.get(
      `/${collectionName}:get?${params.toString()}`
    );
    return response.data.data;
  }

  async createRecord(collectionName: string, data: Partial<Record>): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:create`,
      data
    );
    return response.data.data;
  }

  async updateRecord(collectionName: string, id: string | number, data: Partial<Record>): Promise<Record> {
    const response: AxiosResponse<ApiResponse<Record>> = await this.client.post(
      `/${collectionName}:update?filterByTk=${id}`,
      data
    );
    return response.data.data;
  }

  async deleteRecord(collectionName: string, id: string | number): Promise<void> {
    await this.client.post(`/${collectionName}:destroy?filterByTk=${id}`);
  }

  // Routes API
  async listRoutes(options?: { tree?: boolean }): Promise<DesktopRoute[]> {
    const query = new URLSearchParams();
    if (options?.tree) query.set('tree', 'true');
    query.set('sort', 'sort');
    const qs = query.toString();

    // First try accessible routes (user-level)
    const resp1: AxiosResponse<any> = await this.client.get(
      `/desktopRoutes:listAccessible?${qs}`
    );
    let payload = resp1.data as any;
    let data = Array.isArray(payload) ? payload : payload?.data;
    if (!Array.isArray(data)) {
      // try common enveloping shapes
      if (payload?.data?.rows && Array.isArray(payload.data.rows)) data = payload.data.rows;
      else if (payload?.rows && Array.isArray(payload.rows)) data = payload.rows;
      else if (payload?.tree && Array.isArray(payload.tree)) data = payload.tree;
    }
    if (Array.isArray(data) && data.length > 0) return data as DesktopRoute[];

    // Fallback: try full list (admin-level)
    const resp2: AxiosResponse<any> = await this.client.get(
      `/desktopRoutes:list?${qs}`
    );
    payload = resp2.data as any;
    data = Array.isArray(payload) ? payload : payload?.data;
    if (!Array.isArray(data)) {
      if (payload?.data?.rows && Array.isArray(payload.data.rows)) data = payload.data.rows;
      else if (payload?.rows && Array.isArray(payload.rows)) data = payload.rows;
      else if (payload?.tree && Array.isArray(payload.tree)) data = payload.tree;
    }
    return Array.isArray(data) ? (data as DesktopRoute[]) : [];
  }

  async getRoute(id: string | number): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.get(
      `/desktopRoutes:get?filterByTk=${id}`
    );
    return response.data.data;
  }

  async createRoute(route: Partial<DesktopRoute>): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.post(
      "/desktopRoutes:create",
      route
    );
    return response.data.data;
  }

  async updateRoute(id: string | number, updates: Partial<DesktopRoute>): Promise<DesktopRoute> {
    const response: AxiosResponse<ApiResponse<DesktopRoute>> = await this.client.post(
      `/desktopRoutes:update?filterByTk=${id}`,
      updates
    );
    return response.data.data;
  }

  async deleteRoute(id: string | number): Promise<void> {
    // 先获取路由信息，以便删除对应的UI schema
    try {
      const route = await this.getRoute(id);

      // 删除路由记录
      await this.client.post(`/desktopRoutes:destroy?filterByTk=${id}`);

      // 删除对应的UI schema
      if (route.schemaUid) {
        await this.removeUISchema(route.schemaUid);
      }
      if (route.menuSchemaUid) {
        await this.removeUISchema(route.menuSchemaUid);
      }
    } catch (error) {
      // 如果获取路由失败，仍然尝试删除路由记录
      await this.client.post(`/desktopRoutes:destroy?filterByTk=${id}`);
    }
  }

  // 新增：删除UI schema的方法
  async removeUISchema(schemaUid: string): Promise<void> {
    await this.client.post(`/uiSchemas:remove/${schemaUid}`);
  }

  async moveRoute(options: {
    sourceId: string | number;
    targetId?: string | number;
    targetScope?: any;
    sortField?: string;
    sticky?: boolean;
    method?: 'insertAfter' | 'prepend';
  }): Promise<void> {
    await this.client.post("/desktopRoutes:move", options);
  }

  // 新增：移动菜单项到指定位置的方法
  async moveMenuItemSchema(sourceSchemaUid: string, targetParentUid: string, position: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd' = 'beforeEnd'): Promise<void> {
    try {
      // 首先获取源schema
      const sourceSchema = await this.getPageSchema(sourceSchemaUid);

      // 删除原位置的schema
      await this.removeUISchema(sourceSchemaUid);

      // 在新位置插入schema
      await this.createMenuItemSchema(targetParentUid, sourceSchema, position);
    } catch (error) {
      console.warn('Failed to move menu item schema:', error);
      // 如果菜单移动失败，不要阻止路由移动
    }
  }

  // UI Schema API
  async createPageSchema(schemaUid: string, schema: any): Promise<any> {
    // 修正API调用格式：直接传递schema对象，而不是嵌套在另一个对象中
    const schemaWithUid = {
      ...schema,
      'x-uid': schemaUid
    };

    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insert",
      schemaWithUid
    );
    return response.data.data;
  }

  // 新增：创建菜单项schema的方法
  async createMenuItemSchema(parentUid: string, menuSchema: any, position: 'beforeBegin' | 'afterBegin' | 'beforeEnd' | 'afterEnd' = 'beforeEnd'): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:insertAdjacent/${parentUid}?position=${position}`,
      {
        schema: menuSchema
      }
    );
    return response.data.data;
  }

  async getPageSchema(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getJsonSchema/${schemaUid}`
    );
    return response.data.data;
  }

  async insertBlockSchema(parentUid: string, blockSchema: any, position?: string): Promise<any> {
    // 对于页面级别的插入，我们需要插入到页面的 grid 属性中
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insertAdjacent",
      {
        parentUid,
        schema: blockSchema,
        position: position || 'beforeEnd',
        wrap: null // 确保不包装
      }
    );
    return response.data.data;
  }

  // Generic raw GET helper (for debugging/probing APIs)
  async rawGet(path: string): Promise<any> {
    const url = path.startsWith('/') ? path.slice(1) : path;
    const resp = await this.client.get(url);
    return resp.data;
  }


  async insertBlockToGrid(pageUid: string, blockSchema: any, position?: string): Promise<any> {
    // 智能插入：优先插入到现有的 Grid.Col；若没有行/列，则在 Grid 下创建一行一列并把区块放进去
    const propsResp: AxiosResponse<ApiResponse<any>> = await this.client.get(`/uiSchemas:getProperties/${pageUid}`);
    const root = propsResp.data?.data as any;
    const grid = root?.properties?.grid;

    // Helper: find first Grid.Row and Grid.Col
    function findFirstCol(g: any): string | null {
      if (!g || typeof g !== 'object' || !g.properties) return null;
      for (const [rowKey, rowObj] of Object.entries(g.properties)) {
        if ((rowObj as any)['x-component'] === 'Grid.Row' && (rowObj as any).properties) {
          for (const [colKey, colObj] of Object.entries((rowObj as any).properties)) {
            if ((colObj as any)['x-component'] === 'Grid.Col' && (colObj as any)['x-uid']) {
              return (colObj as any)['x-uid'];
            }
          }
        }
      }
      return null;
    }

    // Case 1: grid exists and has a column -> insert block into that column
    const existingColUid = findFirstCol(grid);
    if (existingColUid) {
      const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
        "/uiSchemas:insertAdjacent",
        {
          parentUid: existingColUid,
          schema: blockSchema,
          position: position || 'beforeEnd'
        }
      );
      return response.data.data;
    }

    // Case 2: grid exists but no rows/cols -> create Row/Col wrapper and include block inside
    if (grid && grid['x-uid']) {
      const rowUid = (Math.random().toString(36).slice(2));
      const colUid = (Math.random().toString(36).slice(2));
      const blockUid = blockSchema['x-uid'] || (Math.random().toString(36).slice(2));
      const wrappedSchema = {
        type: 'void',
        name: rowUid,
        'x-uid': rowUid,
        'x-component': 'Grid.Row',
        properties: {
          [colUid]: {
            type: 'void',
            name: colUid,
            'x-uid': colUid,
            'x-component': 'Grid.Col',
            properties: {
              [blockUid]: {
                ...blockSchema,
                name: blockUid
              }
            }
          }
        }
      };

      const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
        "/uiSchemas:insertAdjacent",
        {
          parentUid: grid['x-uid'],
          schema: wrappedSchema,
          position: position || 'beforeEnd'
        }
      );
      return response.data.data;
    }

    // Fallback: insert block directly under page root (will likely not render ideally but keeps operation safe)
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      "/uiSchemas:insertAdjacent",
      {
        parentUid: pageUid,
        schema: blockSchema,
        position: position || 'beforeEnd'
      }
    );
    return response.data.data;
  }

  async updateBlockSchema(blockUid: string, updates: any): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.post(
      `/uiSchemas:patch`,
      {
        'x-uid': blockUid,
        ...updates
      }
    );
    return response.data.data;
  }

  async deleteBlockSchema(blockUid: string): Promise<void> {
    await this.client.post(`/uiSchemas:remove`, {
      'x-uid': blockUid
    });
  }

  async getSchemaProperties(schemaUid: string): Promise<any> {
    const response: AxiosResponse<ApiResponse<any>> = await this.client.get(
      `/uiSchemas:getProperties/${schemaUid}`
    );
    return response.data.data;
  }
}
