#!/usr/bin/env node

const { spawn } = require('child_process');

// 发送MCP请求的辅助函数
function sendMCPRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const mcp = spawn('node', ['dist/index.js', '--base-url', 'http://**************:13000/api', '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY', '--app', 'mcp_playground'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    mcp.stdout.on('data', (data) => {
      const text = data.toString();
      if (text.includes('MCP Server for NocoBase is running...')) {
        // 服务器启动后发送请求
        const request = {
          jsonrpc: "2.0",
          id: 1,
          method: method,
          params: params
        };
        mcp.stdin.write(JSON.stringify(request) + '\n');
        mcp.stdin.end();
      } else {
        output += text;
      }
    });

    mcp.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    mcp.on('close', (code) => {
      if (output.trim()) {
        try {
          const response = JSON.parse(output.trim());
          resolve(response);
        } catch (e) {
          resolve({ result: output });
        }
      } else if (code === 0) {
        resolve({ success: true });
      } else {
        reject(new Error(`Process exited with code ${code}: ${errorOutput}`));
      }
    });

    // 超时处理
    setTimeout(() => {
      mcp.kill();
      reject(new Error('Request timeout'));
    }, 10000);
  });
}

async function testMenuManagement() {
  console.log('🧹 开始清理菜单...\n');

  // 1. 删除不需要的路由
  const routesToDelete = [13, 15, 17, 18];
  
  for (const routeId of routesToDelete) {
    try {
      console.log(`删除路由 ID: ${routeId}...`);
      const result = await sendMCPRequest('tools/call', {
        name: 'delete_route',
        arguments: { id: routeId }
      });
      console.log(`✓ 路由 ${routeId} 删除成功`);
    } catch (error) {
      console.error(`❌ 删除路由 ${routeId} 失败:`, error.message);
    }
  }

  console.log('\n🏗️ 开始创建新菜单...\n');

  // 2. 创建 page_2
  try {
    console.log('创建 page_2...');
    const result = await sendMCPRequest('tools/call', {
      name: 'create_page_route',
      arguments: {
        title: 'page_2',
        icon: 'FileOutlined',
        template: 'blank'
      }
    });
    console.log('✓ page_2 创建成功');
  } catch (error) {
    console.error('❌ 创建 page_2 失败:', error.message);
  }

  // 3. 创建 group_2
  let group2Id;
  try {
    console.log('创建 group_2...');
    const result = await sendMCPRequest('tools/call', {
      name: 'create_group_route',
      arguments: {
        title: 'group_2',
        icon: 'FolderOutlined'
      }
    });
    console.log('✓ group_2 创建成功');
    
    // 从结果中提取group_2的ID
    if (result.result && result.result.content && result.result.content[0]) {
      const content = result.result.content[0].text;
      const match = content.match(/"id":(\d+)/);
      if (match) {
        group2Id = parseInt(match[1]);
        console.log(`  group_2 ID: ${group2Id}`);
      }
    }
  } catch (error) {
    console.error('❌ 创建 group_2 失败:', error.message);
  }

  // 4. 在 group_2 下创建 group2_group1
  if (group2Id) {
    try {
      console.log('在 group_2 下创建 group2_group1...');
      const result = await sendMCPRequest('tools/call', {
        name: 'create_group_route',
        arguments: {
          title: 'group2_group1',
          parentId: group2Id,
          icon: 'FolderOpenOutlined'
        }
      });
      console.log('✓ group2_group1 创建成功');
    } catch (error) {
      console.error('❌ 创建 group2_group1 失败:', error.message);
    }
  }

  console.log('\n🎉 菜单管理测试完成！');
}

testMenuManagement().catch(console.error);
