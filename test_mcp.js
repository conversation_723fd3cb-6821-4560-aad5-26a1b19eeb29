#!/usr/bin/env node

const { spawn } = require('child_process');

// 创建一个简单的MCP客户端来测试
function sendMCPRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const mcp = spawn('node', ['dist/index.js', '--base-url', 'http://**************:13000/api', '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY', '--app', 'mcp_playground'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    mcp.stdout.on('data', (data) => {
      output += data.toString();
    });

    mcp.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    mcp.on('close', (code) => {
      if (code === 0) {
        resolve(output);
      } else {
        reject(new Error(`Process exited with code ${code}: ${errorOutput}`));
      }
    });

    // 发送MCP请求
    const request = {
      jsonrpc: "2.0",
      id: 1,
      method: method,
      params: params
    };

    mcp.stdin.write(JSON.stringify(request) + '\n');
    mcp.stdin.end();
  });
}

async function testCreatePageRoute() {
  try {
    console.log('Testing create_page_route...');
    
    const timestamp = Date.now();
    const result = await sendMCPRequest('tools/call', {
      name: 'create_page_route',
      arguments: {
        title: `测试页面修正版 ${timestamp}`,
        icon: 'BugOutlined',
        template: 'blank'
      }
    });
    
    console.log('✓ Success:', result);
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

testCreatePageRoute();
