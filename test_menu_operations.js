#!/usr/bin/env node

const { spawn } = require('child_process');

// 发送MCP请求的辅助函数
function sendMCPRequest(method, params = {}) {
  return new Promise((resolve, reject) => {
    const mcp = spawn('node', ['dist/index.js', '--base-url', 'http://**************:13000/api', '--token', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY', '--app', 'mcp_playground'], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    mcp.stdout.on('data', (data) => {
      const text = data.toString();
      if (text.includes('MCP Server for NocoBase is running...')) {
        // 服务器启动后发送请求
        const request = {
          jsonrpc: "2.0",
          id: 1,
          method: method,
          params: params
        };
        mcp.stdin.write(JSON.stringify(request) + '\n');
        mcp.stdin.end();
      } else {
        output += text;
      }
    });

    mcp.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    mcp.on('close', (code) => {
      if (output.trim()) {
        try {
          const response = JSON.parse(output.trim());
          resolve(response);
        } catch (e) {
          resolve({ result: output });
        }
      } else if (code === 0) {
        resolve({ success: true });
      } else {
        reject(new Error(`Process exited with code ${code}: ${errorOutput}`));
      }
    });

    // 超时处理
    setTimeout(() => {
      mcp.kill();
      reject(new Error('Request timeout'));
    }, 15000);
  });
}

async function testMenuOperations() {
  console.log('🧪 测试菜单删除和移动功能\n');

  try {
    // 1. 查看当前菜单结构
    console.log('📋 当前菜单结构:');
    const listResult = await sendMCPRequest('tools/call', {
      name: 'list_routes',
      arguments: {}
    });
    console.log('当前路由列表获取完成\n');

    // 2. 测试删除功能 - 删除 group2_group1_page1 (ID: 24)
    console.log('🗑️ 测试删除功能: 删除 group2_group1_page1...');
    const deleteResult = await sendMCPRequest('tools/call', {
      name: 'delete_route',
      arguments: { id: 24 }
    });
    console.log('✓ 删除操作完成');

    // 3. 测试移动功能 - 将 page_2 移动到 group_1 内部
    console.log('📦 测试移动功能: 将 page_2 移动到 group_1 内部...');
    const moveResult = await sendMCPRequest('tools/call', {
      name: 'move_route',
      arguments: {
        sourceId: 20,
        targetId: 3,
        position: 'inside'
      }
    });
    console.log('✓ 移动操作完成');

    // 4. 测试移动功能 - 将 group2_group1 移动到 page_1 后面
    console.log('📦 测试移动功能: 将 group2_group1 移动到 page_1 后面...');
    const moveResult2 = await sendMCPRequest('tools/call', {
      name: 'move_route',
      arguments: {
        sourceId: 23,
        targetId: 1,
        position: 'after'
      }
    });
    console.log('✓ 移动操作完成');

    console.log('\n🎉 所有测试完成！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

testMenuOperations();
