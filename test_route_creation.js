#!/usr/bin/env node

const { NocoBaseClient } = require('./dist/client.js');

async function testRouteCreation() {
  const client = new NocoBaseClient({
    baseURL: 'http://103.121.94.113:13000/api',
    appId: 'mcp_playground',
    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VySWQiOjEsInJvbGVOYW1lIjoiYWRtaW4iLCJpYXQiOjE3NTQ2MjE2OTIsImV4cCI6MzMzMTIyMjE2OTJ9.C-6noFtpeBx8GcnToz2bzFo0YsaDzFWgFYVV8bs7-NY'
  });

  try {
    console.log('Testing route creation with menu schema...');
    
    // 生成唯一ID
    const timestamp = Date.now();
    const pageSchemaUid = `test-page-${timestamp}`;
    const menuSchemaUid = `test-menu-${timestamp}`;
    
    // 1. 创建页面schema
    const pageSchema = {
      type: "void",
      "x-component": "Page",
      properties: {
        grid: {
          type: "void",
          "x-component": "Grid",
          "x-initializer": "page:addBlock"
        }
      }
    };
    
    console.log('1. Creating page schema...');
    await client.createPageSchema(pageSchemaUid, pageSchema);
    console.log('✓ Page schema created');
    
    // 2. 创建路由记录
    const pageRoute = {
      type: "page",
      title: `测试页面 ${timestamp}`,
      schemaUid: pageSchemaUid,
      menuSchemaUid: menuSchemaUid,
      icon: "TestTubeOutlined",
      enableTabs: false,
      hidden: false
    };
    
    console.log('2. Creating route record...');
    const routeResult = await client.createRoute(pageRoute);
    console.log('✓ Route record created:', routeResult.id);
    
    // 3. 创建菜单项schema
    const menuItemSchema = {
      type: "void",
      title: `测试页面 ${timestamp}`,
      'x-component': "Menu.Item",
      'x-designer': "Menu.Item.Designer",
      'x-component-props': {
        icon: "TestTubeOutlined",
        __route__: routeResult
      },
      'x-uid': menuSchemaUid,
      'x-async': false
    };
    
    console.log('3. Creating menu item schema...');
    await client.createMenuItemSchema("nocobase-admin-menu", menuItemSchema);
    console.log('✓ Menu item schema created');
    
    console.log('\n🎉 Test completed successfully!');
    console.log(`Created route with ID: ${routeResult.id}`);
    console.log(`Page schema UID: ${pageSchemaUid}`);
    console.log(`Menu schema UID: ${menuSchemaUid}`);
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('Response data:', error.response.data);
    }
  }
}

testRouteCreation();
